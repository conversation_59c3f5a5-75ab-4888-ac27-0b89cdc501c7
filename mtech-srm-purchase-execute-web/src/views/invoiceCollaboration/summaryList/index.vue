<template>
  <!-- 发票协同（采方）列表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :hidden-tabs="true"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    />
    <!-- 附件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
    <!-- 发票关联单据明细行弹框 -->
    <bill-details-table-dialog ref="billDetailsTableDialog"></bill-details-table-dialog>
    <!-- 发票回退弹框 -->
    <send-back-dialog ref="sendBackDialog" @confirm="sendBackDialogConfirm"></send-back-dialog>
  </div>
</template>

<script>
import { ColumnData, Toolbar, Table, TabCode } from './config/constant'
import { ConstantType } from '../detailV2/config/constant'
import { formatTableColumnData, serializeList } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import BillDetailsTableDialog from '@/components/businessComponents/billDetailsTableDialog'
import { BillDetailsTableDialogActionType } from '@/components/businessComponents/billDetailsTableDialog/config/constant'
import SendBackDialog from './components/sendBackDialog.vue'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: {
    UploaderDialog,
    BillDetailsTableDialog,
    SendBackDialog
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      currentPushData: null, // 当前推送的数据（发票ID列表和选中记录）
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: Toolbar,
          gridId: this.$tableUUID.invoiceCollaboration.summaryV2.specail,
          grid: {
            lineSelection: 0,
            lineIndex: 1,
            columnData: formatTableColumnData({
              table: Table.summary,
              data: ColumnData
            }),
            dataSource: [],
            // 租户级-发票协同-采方-采方采购-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/customer-reconciliation-invoice-v2/customer-purchaser-paged-special`,
              defaultRules: [],
              serializeList
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const selectedRecords = e.gridRef.getMtechGridRecords()
      const commonToolbar = [
        'Add',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal',
        'export'
      ]
      if (selectedRecords.length == 0 && !commonToolbar.includes(e.toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }

      const idList = []
      selectedRecords.forEach((item) => {
        idList.push(item.id)
      })

      if (e.toolbar.id === 'PushReimbursement') {
        // 推送报销
        this.handlePushReimbursement({ selectedRecords, idList })
      } else if (e.toolbar.id === 'PushPreMakeInvoice') {
        // 推送预制发票
        this.handlePushPreMakeInvoice({ selectedRecords, idList })
      } else if (e.toolbar.id === 'export') {
        // 导出
        this.handleExport()
      }
    },
    handleExport() {
      const asyncParams = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.asyncParams || {}
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let obj = JSON.parse(
        sessionStorage.getItem(this.$tableUUID.invoiceCollaboration.summaryV2.specail)
      )?.visibleCols
      let includeColumnFiledNames = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            includeColumnFiledNames.push(item.field)
          }
        })
      } else {
        ColumnData.forEach((item) => {
          if (item.code) {
            includeColumnFiledNames.push(item.code)
          }
        })
      }
      const params = {
        includeColumnFiledNames,
        ...asyncParams,
        ...queryBuilderRules
      }
      this.$store.commit('startLoading')
      this.$API.invoiceCollaboration.exportInvoiceSummaryApi(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 推送报销
    handlePushReimbursement(args) {
      const { idList, selectedRecords } = args

      // 验证选中数据的公司编码是否一致
      const firstCompanyCode = selectedRecords[0].companyCode || ''
      const hasInconsistentCompanyCode = selectedRecords.some(
        (record) => (record.companyCode || '') !== firstCompanyCode
      )

      if (hasInconsistentCompanyCode) {
        this.$toast({
          content: this.$t('只能选择相同公司编码的数据进行推送报销'),
          type: 'warning'
        })
        return
      }

      // 保存当前选中的发票数据，供人员选择确认后使用
      this.currentPushData = { idList, selectedRecords }

      // 获取选中行的公司编码（已验证一致）
      const companyCode = firstCompanyCode

      // 先查询字典并匹配人员，然后弹出人员选择弹框
      this.loadGxfkDictAndShowDialog(companyCode)
    },
    // 加载GXFK字典并显示人员选择弹框
    async loadGxfkDictAndShowDialog(companyCode) {
      try {
        // 查询GXFK层级字典
        const dictRes = await this.$API.masterData.getDictItemTree({ dictCode: 'GXFK' })
        let matchedPersonnel = null

        if (dictRes && dictRes.data) {
          // 从字典中查找匹配的人员
          const matchedDictItem = this.findDictItemByCompanyCode(dictRes.data, companyCode)

          if (matchedDictItem && matchedDictItem.itemCode) {
            // 根据字典中的人员编码获取人员详情
            const personnelCodes = matchedDictItem.itemCode.split(',').map((code) => code.trim())
            const personnelPromises = personnelCodes.map((code) =>
              this.$API.masterData.getCurrentTenantEmployees({ employeeCode: code })
            )

            const personnelResults = await Promise.all(personnelPromises)
            const companyPersonnel = []

            personnelResults.forEach((res) => {
              if (res && res.data && res.data.length > 0) {
                const person = res.data[0]
                companyPersonnel.push({
                  ...person,
                  employeeId: person.employeeId || person.id,
                  employeeName: person.employeeName,
                  employeeCode: person.employeeCode,
                  departmentOrgName: person.departmentOrgName || person.orgName,
                  companyCode: person.companyCode || person.orgCode
                })
              }
            })

            // 如果找到人员，设置为匹配的人员
            if (companyPersonnel.length > 0) {
              matchedPersonnel = companyPersonnel[0] // 取第一个匹配的人员
            }
          }
        }

        // 弹出人员选择弹框，传入匹配到的人员
        this.$dialog({
          modal: () => import('./components/personnelSelectDialog.vue'),
          data: {
            title: this.$t('推送报销'),
            matchedPersonnel: matchedPersonnel // 传入匹配到的人员
          },
          success: (selectedPersonnel) => {
            this.handlePersonnelSelected(selectedPersonnel)
          }
        })
      } catch (error) {
        // 字典查询失败时，直接弹出弹框
        this.$dialog({
          modal: () => import('./components/personnelSelectDialog.vue'),
          data: {
            title: this.$t('推送报销'),
            matchedPersonnel: null
          },
          success: (selectedPersonnel) => {
            this.handlePersonnelSelected(selectedPersonnel)
          }
        })
      }
    },

    // 递归查找层级字典中的匹配项
    findDictItemByCompanyCode(dictItems, companyCode) {
      if (!dictItems || !Array.isArray(dictItems)) {
        return null
      }

      for (const item of dictItems) {
        // 检查当前项的children中是否有匹配的公司编码
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          // 查找children中是否有匹配的itemCode
          const matchedChild = item.children.find((child) => child.itemCode === companyCode)
          if (matchedChild) {
            // 如果找到匹配的子项，返回外层项（包含人员信息的itemCode和itemName）
            return item
          }

          // 递归检查更深层的子项
          const found = this.findDictItemByCompanyCode(item.children, companyCode)
          if (found) {
            return found
          }
        }
      }

      return null
    },

    // 人员选择确认处理
    handlePersonnelSelected(selectedPersonnel) {
      if (!this.currentPushData) {
        this.$toast({ content: this.$t('推送数据异常，请重新操作'), type: 'error' })
        return
      }

      const { idList } = this.currentPushData

      // 根据接口文档构建推送报销参数
      // 参数格式：数组，每个元素包含 id、employeeCode、employeeName
      const reimbursementParams = idList.map((id) => ({
        id: id,
        employeeCode: selectedPersonnel.employeeCode,
        employeeName: selectedPersonnel.employeeName
      }))

      // 调用推送报销接口
      this.postPushSharedFinanceCarryEmployee(reimbursementParams)

      // 清空当前推送数据
      this.currentPushData = null
    },

    // 处理采购确认人员选择
    async handleConfirmInvoiceWithPersonnel(data) {
      const companyCode = data.companyCode

      if (!companyCode) {
        this.$toast({ content: this.$t('获取公司编码失败'), type: 'error' })
        return
      }

      try {
        // 查询GXFK层级字典
        const dictRes = await this.$API.masterData.getDictItemTree({ dictCode: 'GXFK' })
        let matchedPersonnel = null

        if (dictRes && dictRes.data) {
          // 从字典中查找匹配的人员
          const matchedDictItem = this.findDictItemByCompanyCode(dictRes.data, companyCode)

          if (matchedDictItem && matchedDictItem.itemCode) {
            // 根据字典中的人员编码获取人员详情
            const personnelCodes = matchedDictItem.itemCode.split(',').map((code) => code.trim())
            const personnelPromises = personnelCodes.map((code) =>
              this.$API.masterData.getCurrentTenantEmployees({ employeeCode: code })
            )

            const personnelResults = await Promise.all(personnelPromises)
            const companyPersonnel = []

            personnelResults.forEach((res) => {
              if (res && res.data && res.data.length > 0) {
                const person = res.data[0]
                companyPersonnel.push({
                  ...person,
                  employeeId: person.employeeId || person.id,
                  employeeName: person.employeeName,
                  employeeCode: person.employeeCode,
                  departmentOrgName: person.departmentOrgName || person.orgName,
                  companyCode: person.companyCode || person.orgCode
                })
              }
            })

            // 如果找到人员，设置为匹配的人员
            if (companyPersonnel.length > 0) {
              matchedPersonnel = companyPersonnel[0] // 取第一个匹配的人员
            }
          }
        }

        // 弹出人员选择弹框，传入匹配到的人员
        this.$dialog({
          modal: () => import('./components/personnelSelectDialog.vue'),
          data: {
            title: this.$t('采购确认'),
            matchedPersonnel: matchedPersonnel // 传入匹配到的人员
          },
          success: (selectedPersonnel) => {
            this.handleConfirmInvoicePersonnelSelected(selectedPersonnel, data)
          }
        })
      } catch (error) {
        // 字典查询失败时，直接弹出弹框
        this.$dialog({
          modal: () => import('./components/personnelSelectDialog.vue'),
          data: {
            title: this.$t('采购确认人员'),
            matchedPersonnel: null
          },
          success: (selectedPersonnel) => {
            this.handleConfirmInvoicePersonnelSelected(selectedPersonnel, data)
          }
        })
      }
    },

    // 采购确认人员选择确认处理
    handleConfirmInvoicePersonnelSelected(selectedPersonnel, invoiceData) {
      // 构建采购确认参数，包含人员信息
      const params = {
        id: invoiceData.id,
        pass: true,
        employeeCode: selectedPersonnel.employeeCode,
        employeeName: selectedPersonnel.employeeName
      }

      // 调用采购确认接口
      this.postCustomerReconciliationInvoiceV2purchaserConfirm(params)
    },
    // 推送预制发票
    handlePushPreMakeInvoice(args) {
      const { idList } = args
      // const { valid, invoiceStatus, syncStatus } = this.verifyInvoiceStatus({
      //   data: selectedRecords,
      //   checkStatus: true,
      //   checkSyncStatus: true,
      // });
      // if (
      //   !valid ||
      //   invoiceStatus != InvoiceStatus.complete ||
      //   syncStatus != SyncStatus.notSynced
      // ) {
      //   this.$toast({
      //     content: this.$t("请选择财务已确认且未同步的数据"),
      //     type: "warning",
      //   });
      // } else {
      //   // 状态: 财务已确认 && 推送预制发票状态: 未推送
      // }
      this.postPushPreMakeInvoice({ ids: idList })
    },
    // CellTool
    handleClickCellTool(args) {
      const { data, tool } = args
      if (tool.id === 'SendBack' || tool.id === 'SendBack1') {
        // 退回
        this.$refs.sendBackDialog.dialogInit({
          title: this.$t('确定退回'),
          selectData: data
        })
      } else if (tool.id === 'ConfirmInvoice') {
        // 采购确认 - 先弹出人员选择
        this.handleConfirmInvoiceWithPersonnel(data)
      } else if (tool.id === 'ConfirmInvoiceFinance') {
        // 财务确认
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确定财务确认选中的数据？')
          },
          success: () => {
            const params = {
              id: data.id,
              pass: true
              // rejectReason: "",
            }
            // 采方财务确认
            this.postCustomerReconciliationInvoiceV2FinanceConfirm(params)
          }
        })
      }
    },
    // 对账单外部接口-对账单推送第三方共享财务
    postCustomerReconciliationInvoiceV2PushSharedFinance(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2PushSharedFinance(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 发票推送预制发票
    postPushPreMakeInvoice(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postPushPreMadInvoice(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方采购确认
    postCustomerReconciliationInvoiceV2purchaserConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2purchaserConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方财务确认
    postCustomerReconciliationInvoiceV2FinanceConfirm(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FinanceConfirm(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 确定退回 弹框 调用api完成
    sendBackDialogConfirm() {
      // 刷新当前 Grid
      this.refreshColumns()
    },
    // 验证 发票、同步 状态是否统一
    verifyInvoiceStatus(args) {
      const { data, checkStatus, checkSyncStatus } = args
      let valid = true
      let invoiceStatus = null
      let syncStatus = null
      data.forEach((item, index) => {
        invoiceStatus = item.status // 发票状态
        syncStatus = item.syncStatus // 同步状态

        if (checkStatus && checkSyncStatus) {
          // 校验 发票状态、同步状态
          if (
            item &&
            data[index - 1] &&
            (item.status !== data[index - 1].status ||
              item.syncStatus !== data[index - 1].syncStatus)
          ) {
            valid = false
          }
        }
      })

      return { valid, invoiceStatus, syncStatus }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data, fieldValue } = args
      if (field === 'reconciliationHeaderCodes') {
        // 点击 关联对账单号
        this.handleReconciliationHeaderCodes({
          data,
          fieldValue
        })
      } else if (field === 'attachementCount') {
        // 点击 发票附件 数量
        this.handleAttachementCount({ data })
      } else if (field === 'reconciliationItemCount') {
        // 点击 关联明细 数量
        this.handleReconciliationItemCountClick(data)
      }
    },
    // 点击 关联对账单号
    handleReconciliationHeaderCodes(args) {
      const { data, fieldValue: reconciliationCode } = args
      // 从 发票列表 跳转 单据详情页
      const reconciliationHeaderIdIndex = data.reconciliationHeaderCodes.findIndex(
        (item) => item === reconciliationCode
      )
      // 请求 api 根据单据 code、id 获取单据数据
      const params = {
        page: { current: 1, size: 10 },
        condition: 'and',
        defaultRules: [
          {
            field: 'reconciliationCode',
            type: 'string',
            operator: 'equal',
            value: reconciliationCode
          },
          {
            field: 'id',
            type: 'string',
            operator: 'equal',
            value: data.reconciliationHeaderIds[reconciliationHeaderIdIndex].buyerHeaderId
          }
        ]
      }
      this.apiStartLoading()
      // 获取单据信息 租户级-发票协同-采方主单-对账信息
      this.$API.invoiceCollaboration
        .postReconciliationHeaderQueryBuilder(params)
        .then((res) => {
          // this.apiEndLoading();
          const reconciliationHeaderInfo = res?.data?.records[0] || {}
          // 从 可开票单据 跳转 单据详情页
          this.goToInvoiceDetail({
            headerInfo: reconciliationHeaderInfo,
            entryType: ConstantType.look,
            status: data.status,
            id: data.id
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 发票附件 数量
    handleAttachementCount(args) {
      const { data } = args
      // 获取发票附件数据 后 显示 发票附件列表 弹框
      const params = {
        id: data.id
      }
      this.apiStartLoading()
      // 获取发票附件列表 查询附件列表
      this.$API.invoiceCollaboration
        .postCustomerReconciliationInvoiceV2FileList(params)
        .then((res) => {
          this.apiEndLoading()
          const fileData = res?.data || []
          // 显示附件查看弹框
          const dialogParams = {
            fileData: fileData,
            isView: true,
            title: this.$t('发票附件')
          }
          this.$refs.uploaderDialog.dialogInit(dialogParams)
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 点击 关联明细 数量
    handleReconciliationItemCountClick(data) {
      const params = {
        businessTypeCode: data.reconciliationBusinessTypeCode,
        reconciliationTypeCode: data.reconciliationTypeCode
      }
      this.apiStartLoading()
      // 获取 明细行 表头 采购对账字段配置
      this.$API.reconciliationSettlement
        .postReconciliationConfigFieldQuery(params)
        .then((res) => {
          // this.apiEndLoading();
          const fieldList = res?.data || []
          const fieldConfig = fieldList.find(
            (item) => item.code === TabCode.reconciliationField && item.checkStatus
          )
          const fields = fieldConfig?.fieldResponseList || []
          const columnData = formatTableColumnData({
            table: Table.statementDetails,
            data: fields
          })
          // 显示明细行列表弹框 查看关联 明细行、高低开
          this.$refs.billDetailsTableDialog.dialogInit({
            title: this.$t('关联明细行列表'),
            actionType: BillDetailsTableDialogActionType.view,
            dataItemAsyncConfig: {
              // 新增发票时，dataId传0、defaultRule里面传headerId即可；
              // 修改发票时，dataId传发票id，defaultRule里面传headerId，能查出来关联了发票的明细行以及未关联发票的明细行
              // 查看发票明细行时，dataId传发票id，在defaultRule里面再传invoiceId对应当前发票id，只查出来当前发票关联的明细行
              url: `${BASE_TENANT}/customer-reconciliation-invoice-v2/item-page-with-invoice`, // 发票协同-供方-查询对账明细
              condition: 'and',
              params: {
                dataId: data.id // 发票新增时传 0，编辑、查看时传 发票id
              },
              defaultRules: [
                {
                  field: 'invoiceId',
                  operator: 'equal',
                  value: data.id
                }
              ]
            },
            dataItemColumnData: columnData,
            // 高低开
            highLowInfoAsyncConfig: {
              url: `${BASE_TENANT}/reconciliationHighLow/queryByInvoiceId`,
              params: {
                id: data.id // 发票id
              },
              recordsPosition: 'data'
            }
          })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 跳转到 发票详情页面
    goToInvoiceDetail(data) {
      const { headerInfo, entryType, status, id } = data
      // 发票详情 页面参数
      const params = {
        reconciliationCode: headerInfo.reconciliationCode, // 对账单号
        headerInfo, // 头部信息 行数据
        entryType // 页面类型
      }
      localStorage.setItem('summaryV2Data', JSON.stringify(params))

      const isGeneral = headerInfo.businessTypeCode === 'BTTCL004' ? true : false // 是否通采 待修改
      this.$router.push({
        name: isGeneral ? 'invoice-detail-v2' : 'invoice-detail',
        query: { fromType: 'list-special', status, id }
      })
    },

    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 推送报销接口调用
    postPushSharedFinanceCarryEmployee(params) {
      this.apiStartLoading()
      this.$API.invoiceCollaboration
        .postPushSharedFinanceCarryEmployee(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('推送报销成功'), type: 'success' })
            // 刷新当前 Grid
            this.$refs.templateRef.refreshCurrentGridData()
          } else {
            this.$toast({ content: res?.msg || this.$t('推送报销失败'), type: 'error' })
          }
        })
        .catch((error) => {
          this.apiEndLoading()
          this.$toast({ content: error.msg || this.$t('推送报销失败'), type: 'error' })
        })
    },

    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>
