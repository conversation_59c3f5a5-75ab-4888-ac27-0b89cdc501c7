

## 非采对账单推送第三方共享财务-携带员工信息


**接口地址**:`/api/srm-purchase-execute/tenant/customer-reconciliation-invoice-v2/push-shared-finance-carry-employee`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>非采对账单推送第三方共享财务-携带员工信息</p>



**请求示例**:


```javascript
[
  {
    "employeeCode": "",
    "employeeName": "",
    "id": 0
  }
]
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|reimbursementTranslationRequestList|非采-推送报销|body|true|array|非采-推送报销|
|&emsp;&emsp;employeeCode|员工CODE||false|string||
|&emsp;&emsp;employeeName|员工Name||false|string||
|&emsp;&emsp;id|id||false|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|通用返回统一封装对象«boolean»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|响应编码|integer(int32)|integer(int32)|
|data|数据|boolean||
|errorStackTrace|错误堆栈信息|string||
|msg|提示信息|string||
|success|是否成功|boolean||
|traceId|追踪ID|string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": true,
	"errorStackTrace": "",
	"msg": "",
	"success": true,
	"traceId": ""
}
```