<template>
  <mt-dialog
    ref="dialog"
    :header="title"
    :buttons="buttons"
    :visible="visible"
    width="500"
    height="400"
    @beforeClose="handleCancel"
  >
    <div class="personnel-select-container">
      <!-- 人员选择表单 -->
      <mt-form :model="formData" class="personnel-form">
        <mt-form-item :label="$t('付款人员选择')" label-style="top" prop="selectedPersonnel">
          <RemoteAutocomplete
            v-model="formData.selectedPersonnel"
            url="/masterDataManagement/tenant/employee/paged-query"
            :fields="{ text: 'employeeName', value: 'employeeCode' }"
            :search-fields="['employeeCode', 'employeeName']"
            :placeholder="$t('请选择')"
            @change="handlePersonnelChange"
          />
        </mt-form-item>
      </mt-form>

      <!-- 已选人员显示 -->
      <div class="selected-personnel-info" v-if="selectedPersonnelInfo">
        <div class="info-title">{{ $t('已选人员') }}:</div>
        <div class="info-content">
          <span class="person-name">{{ selectedPersonnelInfo.employeeName }}</span>
          <span class="person-code">({{ selectedPersonnelInfo.employeeCode }})</span>
          <span class="person-dept" v-if="selectedPersonnelInfo.departmentName">
            - {{ selectedPersonnelInfo.departmentName }}
          </span>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  name: 'PersonnelSelectDialog',
  components: {
    RemoteAutocomplete
  },
  props: {
    title: {
      type: String,
      default() {
        return this.$t('付款人员选择')
      }
    },
    companyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      formData: {
        selectedPersonnel: null, // 选中的人员值
        generalPersonnel: null // 通用人员值（单选模式使用单个值）
      },
      personnelOptions: [], // 人员选项（根据公司编码匹配的人员 + 其他选项）
      selectedPersonnelInfo: null, // 最终选中的人员信息
      buttons: [
        {
          click: this.handleCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    // 是否显示通用人员选择
    showGeneralPersonnelSelect() {
      return this.formData.selectedPersonnel === 'other'
    }
  },
  mounted() {
    // 组件挂载后自动显示弹框并加载数据
    this.visible = true
    this.loadPersonnelOptions()
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    // 隐藏弹框
    hide() {
      this.visible = false
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.formData = {
        selectedPersonnel: null,
        generalPersonnel: null // 单选模式使用单个值
      }
      this.selectedPersonnelInfo = null
    },

    // 加载人员选项
    async loadPersonnelOptions() {
      this.loading = true
      try {
        // 获取所有人员数据
        const res = await this.$API.masterData.getCurrentTenantEmployees()
        if (res && res.data) {
          const allPersonnel = res.data.map((item) => ({
            ...item,
            employeeId: item.employeeId || item.id,
            employeeName: item.employeeName,
            employeeCode: item.employeeCode,
            departmentOrgName: item.departmentOrgName || item.orgName,
            companyCode: item.companyCode || item.orgCode
          }))

          // 根据公司编码过滤人员
          const companyPersonnel = allPersonnel.filter(
            (person) => person.companyCode === this.companyCode
          )

          // 构建人员选项
          this.personnelOptions = [
            ...companyPersonnel.map((person) => ({
              label: `${person.employeeName} (${person.employeeCode})`,
              value: person.employeeId,
              data: person
            })),
            {
              label: this.$t('其他'),
              value: 'other',
              data: null
            }
          ]
        }
      } catch (error) {
        console.error('加载人员列表失败:', error)
        this.$toast({ content: this.$t('加载人员列表失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    // 人员选择过滤
    handlePersonnelFilter(args) {
      const { text } = args
      if (!text) return this.personnelOptions

      return this.personnelOptions.filter((option) =>
        option.label.toLowerCase().includes(text.toLowerCase())
      )
    },

    // 人员选择变化处理
    handlePersonnelChange(args) {
      const { value } = args
      if (value === 'other') {
        // 选择"其他"，清空通用人员选择
        this.formData.generalPersonnel = null
        this.selectedPersonnelInfo = null
      } else if (value) {
        // 选择具体人员
        const selectedOption = this.personnelOptions.find((option) => option.value === value)
        if (selectedOption && selectedOption.data) {
          this.selectedPersonnelInfo = selectedOption.data
        }
        // 清空通用人员选择
        this.formData.generalPersonnel = null
      } else {
        this.selectedPersonnelInfo = null
      }
    },

    // 通用人员选择变化处理
    handleGeneralPersonnelChange(args) {
      // RemoteAutocomplete单选模式返回的是选中的数据对象
      if (args && args.value) {
        // args.value 是选中的值，args.itemData 是选中的完整数据对象
        const selectedPerson = args.itemData
        if (selectedPerson) {
          this.selectedPersonnelInfo = {
            employeeId: selectedPerson.employeeId || selectedPerson.id,
            employeeName: selectedPerson.employeeName,
            employeeCode: selectedPerson.employeeCode,
            departmentName: selectedPerson.departmentName
          }
          // 更新formData.generalPersonnel以支持回显
          this.formData.generalPersonnel = args.value
        }
      } else {
        this.selectedPersonnelInfo = null
        this.formData.generalPersonnel = null
      }
    },

    // 确定按钮处理
    handleConfirm() {
      if (!this.selectedPersonnelInfo) {
        this.$toast({ content: this.$t('请选择人员'), type: 'warning' })
        return
      }

      // 通过$emit('confirm-function')返回选中的人员信息，适配$dialog调用方式
      this.$emit('confirm-function', this.selectedPersonnelInfo)
      this.hide()
    },

    // 取消按钮处理
    handleCancel() {
      this.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.personnel-select-container {
  padding: 20px;

  .personnel-form {
    margin-bottom: 20px;
  }

  .selected-personnel-info {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .info-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
    }

    .info-content {
      .person-name {
        font-weight: 500;
        color: #409eff;
      }

      .person-code {
        color: #909399;
        margin-left: 4px;
      }

      .person-dept {
        color: #606266;
        margin-left: 4px;
      }
    }
  }
}
</style>
