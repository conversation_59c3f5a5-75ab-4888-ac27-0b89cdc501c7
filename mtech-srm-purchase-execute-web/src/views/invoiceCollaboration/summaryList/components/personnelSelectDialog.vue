<template>
  <mt-dialog
    ref="dialog"
    :header="dialogTitle"
    :buttons="buttons"
    :visible="visible"
    width="500"
    height="400"
    @beforeClose="handleCancel"
  >
    <div class="personnel-select-container">
      <!-- 人员选择表单 -->
      <mt-form :model="formData" class="personnel-form">
        <mt-form-item :label="$t('付款人员')" label-style="top" prop="selectedPersonnel">
          <RemoteAutocomplete
            v-model="formData.selectedPersonnel"
            url="/masterDataManagement/tenant/employee/paged-query"
            :fields="{ text: 'employeeName', value: 'employeeCode' }"
            :search-fields="['employeeCode', 'employeeName']"
            :placeholder="$t('请选择')"
            @change="handlePersonnelChange"
          />
        </mt-form-item>
      </mt-form>

      <!-- 已选人员显示 -->
      <div class="selected-personnel-info" v-if="selectedPersonnelInfo">
        <div class="info-title">{{ $t('已选人员') }}:</div>
        <div class="info-content">
          <span class="person-name">{{ selectedPersonnelInfo.employeeName }}</span>
          <span class="person-code">({{ selectedPersonnelInfo.employeeCode }})</span>
          <span class="person-dept" v-if="selectedPersonnelInfo.departmentName">
            - {{ selectedPersonnelInfo.departmentName }}
          </span>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'

export default {
  name: 'PersonnelSelectDialog',
  components: {
    RemoteAutocomplete
  },
  props: {
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      formData: {
        selectedPersonnel: null, // 选中的人员值
        generalPersonnel: null // 通用人员值（单选模式使用单个值）
      },
      selectedPersonnelInfo: null, // 最终选中的人员信息
      buttons: [
        {
          click: this.handleCancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  computed: {
    // 是否显示通用人员选择
    showGeneralPersonnelSelect() {
      return this.formData.selectedPersonnel === 'other'
    },
    // 弹框标题
    dialogTitle() {
      return this.modalData?.title || this.$t('付款人员选择')
    },
    // 公司编码
    companyCode() {
      return this.modalData?.companyCode || ''
    },
    // 匹配到的人员
    matchedPersonnel() {
      return this.modalData?.matchedPersonnel || null
    }
  },
  mounted() {
    // 组件挂载后自动显示弹框并初始化数据
    this.visible = true
    console.log('组件挂载时的props:', { title: this.dialogTitle, companyCode: this.companyCode })
    console.log('modalData:', this.modalData)
    this.initPersonnelData()
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    // 隐藏弹框
    hide() {
      this.visible = false
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.formData = {
        selectedPersonnel: null,
        generalPersonnel: null // 单选模式使用单个值
      }
      this.selectedPersonnelInfo = null
    },

    // 初始化人员数据
    initPersonnelData() {
      // 如果有匹配到的人员，直接设置并回显
      if (this.matchedPersonnel) {
        console.log('匹配到的人员:', this.matchedPersonnel)
        this.formData.selectedPersonnel = this.matchedPersonnel.employeeCode
        this.selectedPersonnelInfo = this.matchedPersonnel
      }
    },

    // 人员选择变化处理
    handlePersonnelChange(args) {
      console.log('handlePersonnelChange args:', args)

      // RemoteAutocomplete返回的数据结构：{ value: employeeCode, itemData: 完整人员对象 }
      if (args && args.value) {
        if (args.value === 'other') {
          // 选择"其他"，清空通用人员选择
          this.formData.generalPersonnel = null
          this.selectedPersonnelInfo = null
        } else {
          // 选择具体人员，使用itemData获取完整人员信息
          const selectedPerson = args.itemData
          if (selectedPerson) {
            this.selectedPersonnelInfo = {
              employeeId: selectedPerson.employeeId || selectedPerson.id,
              employeeName: selectedPerson.employeeName,
              employeeCode: selectedPerson.employeeCode,
              departmentName: selectedPerson.departmentName || selectedPerson.departmentOrgName,
              companyCode: selectedPerson.companyCode
            }
          }
          // 清空通用人员选择
          this.formData.generalPersonnel = null
        }
      } else {
        this.selectedPersonnelInfo = null
      }
    },

    // 通用人员选择变化处理
    handleGeneralPersonnelChange(args) {
      // RemoteAutocomplete单选模式返回的是选中的数据对象
      if (args && args.value) {
        // args.value 是选中的值，args.itemData 是选中的完整数据对象
        const selectedPerson = args.itemData
        if (selectedPerson) {
          this.selectedPersonnelInfo = {
            employeeId: selectedPerson.employeeId || selectedPerson.id,
            employeeName: selectedPerson.employeeName,
            employeeCode: selectedPerson.employeeCode,
            departmentName: selectedPerson.departmentName
          }
          // 更新formData.generalPersonnel以支持回显
          this.formData.generalPersonnel = args.value
        }
      } else {
        this.selectedPersonnelInfo = null
        this.formData.generalPersonnel = null
      }
    },

    // 确定按钮处理
    handleConfirm() {
      if (!this.selectedPersonnelInfo) {
        this.$toast({ content: this.$t('请选择人员'), type: 'warning' })
        return
      }

      // 通过$emit('confirm-function')返回选中的人员信息，适配$dialog调用方式
      this.$emit('confirm-function', this.selectedPersonnelInfo)
      this.hide()
    },

    // 取消按钮处理
    handleCancel() {
      this.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
.personnel-select-container {
  padding: 20px;

  .personnel-form {
    margin-bottom: 20px;
  }

  .selected-personnel-info {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .info-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
    }

    .info-content {
      .person-name {
        font-weight: 500;
        color: #409eff;
      }

      .person-code {
        color: #909399;
        margin-left: 4px;
      }

      .person-dept {
        color: #606266;
        margin-left: 4px;
      }
    }
  }
}
</style>
